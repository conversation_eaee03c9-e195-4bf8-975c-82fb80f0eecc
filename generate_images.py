# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import mimetypes
import os
from google import genai
from google.genai import types


def save_binary_file(file_name, data):
    f = open(file_name, "wb")
    f.write(data)
    f.close()
    print(f"File saved to: {file_name}")


def generate_image(prompt, size, output_prefix):
    """
    Generate an image with specified size
    
    Args:
        prompt (str): The text prompt for image generation
        size (str): Image size specification (e.g., "1024x1024")
        output_prefix (str): Prefix for output filename
    """
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.5-flash-image-preview"
    
    # Create the full prompt with size specification
    full_prompt = f"{prompt}. Generate an image with dimensions {size}."
    
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=full_prompt),
            ],
        ),
    ]
    
    generate_content_config = types.GenerateContentConfig(
        response_modalities=[
            "IMAGE",
            "TEXT",
        ],
    )

    print(f"Generating image with size {size}...")
    
    file_index = 0
    for chunk in client.models.generate_content_stream(
        model=model,
        contents=contents,
        config=generate_content_config,
    ):
        if (
            chunk.candidates is None
            or chunk.candidates[0].content is None
            or chunk.candidates[0].content.parts is None
        ):
            continue
        if chunk.candidates[0].content.parts[0].inline_data and chunk.candidates[0].content.parts[0].inline_data.data:
            inline_data = chunk.candidates[0].content.parts[0].inline_data
            data_buffer = inline_data.data
            file_extension = mimetypes.guess_extension(inline_data.mime_type)
            file_name = f"{output_prefix}_{size}_{file_index}{file_extension}"
            file_index += 1
            save_binary_file(file_name, data_buffer)
        else:
            if hasattr(chunk, 'text') and chunk.text:
                print(chunk.text)


def main():
    # Read the prompt from prompt.txt
    with open("prompt.txt", "r", encoding="utf-8") as f:
        prompt = f.read().strip()
    
    # Read the API key from gemini_key.txt
    with open("gemini_key.txt", "r", encoding="utf-8") as f:
        api_key = f.read().strip()
    
    # Set the API key as environment variable
    os.environ["GEMINI_API_KEY"] = api_key
    
    print(f"Using prompt: {prompt}")
    print("Generating images in three different sizes...")
    
    # Define the sizes to generate
    sizes = ["1024x1024", "512x512", "768x768"]
    
    # Generate images for each size
    for size in sizes:
        try:
            generate_image(prompt, size, "generated_image")
            print(f"Successfully generated image with size {size}")
        except Exception as e:
            print(f"Error generating image with size {size}: {str(e)}")
    
    print("Image generation completed!")


if __name__ == "__main__":
    main()
